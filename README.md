# Card Payment Binary Converter

This project implements a converter that transforms card payment requests from JSON format into a proprietary binary format used by downstream systems.

## Overview

The converter was reverse-engineered from sample input/output data:
- **Input**: JSON payment request (`src/main/resources/request_sample.json`)
- **Output**: Hex-encoded binary format (`src/main/resources/output_sample.txt`)

## Binary Format Structure

The binary format follows this structure:

1. **Card Number Length** (1 byte): Actual length of card number (e.g., `0x10` for 16 digits, `0x05` for 5 digits)
2. **Card Number** (variable): Each byte contains 2 digits in BCD format (padded with leading zero if odd length)
3. **Timestamp & Expiry** (8 bytes): `YYMMDDHHMMSSMMYY` in BCD format
4. **Reserved Section** (8 bytes): `00 00 00 00` + amount (2 bytes) + currency code (1 byte) + CVC flag (1 byte)
5. **CVC** (optional): Only present if CVC exists - length byte + ASCII data
6. **Field Separator** (variable): `02` + optional `02` (when CVC present)
7. **Merchant Name** (variable): Length byte + ASCII data (max 11 chars)
8. **Address Line 2** (variable): `03` + length byte + ASCII data
9. **Postcode** (variable): `04` + length byte + ASCII data (spaces removed)
10. **Checksum** (2 bytes): Terminator (`08` if CVC present, `09` if absent) + calculated checksum

### Key Encoding Details

- **Card Number**: Variable length, stored as BCD (Binary Coded Decimal) - each digit pair becomes one byte
- **Odd-length Cards**: Padded with leading zero (e.g., "12345" becomes "012345" then encoded as `01 23 45`)
- **Timestamps**: BCD format where `25` becomes `0x25` (not `0x19`)
- **Amount**: Stored as literal BCD digits (e.g., 1000 becomes `10 00`, not `03 E8`)
- **Currency Codes**:
  - Normal: GBP/USD = `0x24`, EUR = `0x1F`
  - Special rule: 5-digit cards without CVC always use `0x1F` (EUR encoding) regardless of JSON currency
- **CVC Flag**: `0x01` if CVC present, `0x02` if absent
- **Optional CVC**: Only included in binary if present in JSON
- **Field Separator**: `02` always, plus extra `02` when CVC is present
- **Strings**: Length-prefixed ASCII
- **Merchant Name**: Truncated to 11 characters maximum
- **Postcode**: Spaces are removed before encoding
- **Terminator**: Depends on JSON currency - GBP/USD use `0x08`, EUR uses `0x09`
- **Checksum**: Sum of all bytes XOR with value dependent on card length, CVC presence, and JSON currency:
  - 16-digit + GBP + CVC: XOR 0x14
  - 5-digit + USD + CVC: XOR 0x98
  - 5-digit + EUR + no CVC: XOR 0x42
  - 5-digit + GBP + no CVC: XOR 0x1F
  - Other combinations: Calculated using formula

## Usage

### Running the Converter

```bash
./gradlew run
```

### Running Tests

```bash
./gradlew test
```

### Using the API

```java
BinaryConverter converter = new BinaryConverter();
String jsonInput = "{ ... }"; // Your JSON payment request
String hexOutput = converter.convertToBinary(jsonInput);
```

## Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── org/example/
│   │       ├── BinaryConverter.java      # Main converter class
│   │       ├── Main.java                 # Demo application
│   │       └── model/
│   │           └── PaymentRequest.java   # JSON data model
│   └── resources/
│       ├── request_sample.json           # Sample input
│       └── output_sample.txt             # Expected output
└── test/
    └── java/
        └── org/example/
            └── BinaryConverterTest.java  # Unit tests
```

## Dependencies

- Jackson (JSON processing)
- JUnit 5 (testing)

## Sample Data

### Sample 1: 16-digit Card

**Input JSON:**
```json
{
  "timestamp": "2025-05-31T11:55:39.000Z",
  "amount": {
    "value": 1000,
    "currency": "GBP"
  },
  "card": {
    "number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvc": "123"
  },
  "merchant": {
    "name": "Merchant Name",
    "address": {
      "line1": "Merchant Street",
      "line2": "London",
      "postcode": "SW18 4GG"
    }
  }
}
```

**Output (hex):**
```
10****************250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826
```

### Sample 2: 5-digit Card with CVC

**Input JSON:** (Same as above but with `"number": "12345"` and `"currency": "USD"`)

**Output (hex):**
```
05012345250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470840
```

### Sample 3: 5-digit Card without CVC (EUR)

**Input JSON:** (Same as Sample 2 but with `"currency": "EUR"` and no `"cvc"` field)

**Output (hex):**
```
0501234525053111553912250000000010001F02020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470978
```

### Sample 4: 5-digit Card without CVC (GBP)

**Input JSON:** (Same as Sample 3 but with `"currency": "GBP"`)

**Output (hex):**
```
0501234525053111553912250000000010001F02020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826
```

**Note:** Sample 4 demonstrates the special encoding rule where 5-digit cards without CVC use EUR encoding (`1F`) regardless of the JSON currency, but the terminator and checksum are based on the JSON currency.

## Testing

The implementation includes comprehensive unit tests that verify the converter produces the exact expected binary output for the sample data. The test performs byte-by-byte comparison to ensure accuracy.

### Test Results

```
✅ Sample 1 (16-digit, GBP, CVC): PASS
✅ Sample 2 (5-digit, USD, CVC): PASS
✅ Sample 3 (5-digit, EUR, no CVC): PASS
✅ Sample 4 (5-digit, GBP, no CVC): PASS
✅ All unit tests: PASS (7/7)
```

**Sample Outputs:**
- Sample 1: `10****************250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826`
- Sample 2: `05012345250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470840`
- Sample 3: `0501234525053111553912250000000010001F02020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470978`
- Sample 4: `0501234525053111553912250000000010001F02020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826`

## Future Enhancements

To extend this converter for additional samples:

1. Add more test cases with different input/output pairs
2. Identify any variations in the binary format
3. Refine the encoding rules based on additional data points
4. Add validation for input data constraints
