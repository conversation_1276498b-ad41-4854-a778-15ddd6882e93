package org.example;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class BinaryConverterTest {
    private BinaryConverter converter;
    private String sampleJson;
    private String expectedOutput;

    @BeforeEach
    void setUp() throws IOException {
        converter = new BinaryConverter();

        // Load the sample files
        sampleJson = Files.readString(Paths.get("src/main/resources/request_sample.json"));
        expectedOutput = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
    }

    @Test
    void testConvertSampleData() throws IOException {
        String actualOutput = converter.convertToBinary(sampleJson);

        System.out.println("Expected: " + expectedOutput);
        System.out.println("Actual:   " + actualOutput);
        System.out.println("Length expected: " + expectedOutput.length());
        System.out.println("Length actual:   " + actualOutput.length());

        // For now, let's just check that we get some output and analyze the differences
        assertNotNull(actualOutput);
        assertFalse(actualOutput.isEmpty());

        // Let's compare byte by byte to understand the differences
        if (!expectedOutput.equals(actualOutput)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput.length(), actualOutput.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput.substring(i, Math.min(i + 2, expectedOutput.length()));
                String actualByte = actualOutput.substring(i, Math.min(i + 2, actualOutput.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput.length() != actualOutput.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput.length() + ", actual: " + actualOutput.length());
            }
        }

        // Now that we've fixed the issues, let's assert equality
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    void testConvertSample2Data() throws IOException {
        // Load the second sample files
        String sampleJson2 = Files.readString(Paths.get("src/main/resources/request_sample2.json"));
        String expectedOutput2 = Files.readString(Paths.get("src/main/resources/output_sample2.txt")).trim();

        String actualOutput2 = converter.convertToBinary(sampleJson2);

        System.out.println("=== SAMPLE 2 TEST ===");
        System.out.println("Expected: " + expectedOutput2);
        System.out.println("Actual:   " + actualOutput2);
        System.out.println("Length expected: " + expectedOutput2.length());
        System.out.println("Length actual:   " + actualOutput2.length());

        // Compare byte by byte if different
        if (!expectedOutput2.equals(actualOutput2)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput2.length(), actualOutput2.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput2.substring(i, Math.min(i + 2, expectedOutput2.length()));
                String actualByte = actualOutput2.substring(i, Math.min(i + 2, actualOutput2.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput2.length() != actualOutput2.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput2.length() + ", actual: " + actualOutput2.length());
            }
        }

        assertEquals(expectedOutput2, actualOutput2);
    }

    @Test
    void testConvertSample3Data() throws IOException {
        // Load the third sample files
        String sampleJson3 = Files.readString(Paths.get("src/main/resources/request_sample3.json"));
        String expectedOutput3 = Files.readString(Paths.get("src/main/resources/output_sample3.txt")).trim();

        String actualOutput3 = converter.convertToBinary(sampleJson3);

        System.out.println("=== SAMPLE 3 TEST ===");
        System.out.println("Expected: " + expectedOutput3);
        System.out.println("Actual:   " + actualOutput3);
        System.out.println("Length expected: " + expectedOutput3.length());
        System.out.println("Length actual:   " + actualOutput3.length());

        // Compare byte by byte if different
        if (!expectedOutput3.equals(actualOutput3)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput3.length(), actualOutput3.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput3.substring(i, Math.min(i + 2, expectedOutput3.length()));
                String actualByte = actualOutput3.substring(i, Math.min(i + 2, actualOutput3.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput3.length() != actualOutput3.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput3.length() + ", actual: " + actualOutput3.length());
            }
        }

        assertEquals(expectedOutput3, actualOutput3);
    }

    @Test
    void testAllFourSamplesWork() throws IOException {
        // Verify all four samples work correctly
        String sample1Json = Files.readString(Paths.get("src/main/resources/request_sample.json"));
        String expected1 = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
        String actual1 = converter.convertToBinary(sample1Json);

        String sample2Json = Files.readString(Paths.get("src/main/resources/request_sample2.json"));
        String expected2 = Files.readString(Paths.get("src/main/resources/output_sample2.txt")).trim();
        String actual2 = converter.convertToBinary(sample2Json);

        String sample3Json = Files.readString(Paths.get("src/main/resources/request_sample3.json"));
        String expected3 = Files.readString(Paths.get("src/main/resources/output_sample3.txt")).trim();
        String actual3 = converter.convertToBinary(sample3Json);

        String sample4Json = Files.readString(Paths.get("src/main/resources/request_sample4.json"));
        String expected4 = Files.readString(Paths.get("src/main/resources/output_sample4.txt")).trim();
        String actual4 = converter.convertToBinary(sample4Json);

        assertEquals(expected1, actual1, "Sample 1 should match");
        assertEquals(expected2, actual2, "Sample 2 should match");
        assertEquals(expected3, actual3, "Sample 3 should match");
        assertEquals(expected4, actual4, "Sample 4 should match");

        System.out.println("✅ All four samples converted correctly!");
        System.out.println("Sample 1 (16-digit, GBP, CVC): " + (actual1.equals(expected1) ? "PASS" : "FAIL"));
        System.out.println("Sample 2 (5-digit, USD, CVC): " + (actual2.equals(expected2) ? "PASS" : "FAIL"));
        System.out.println("Sample 3 (5-digit, EUR, no CVC): " + (actual3.equals(expected3) ? "PASS" : "FAIL"));
        System.out.println("Sample 4 (5-digit, GBP, no CVC): " + (actual4.equals(expected4) ? "PASS" : "FAIL"));
    }

    @Test
    void testConvertSample4Data() throws IOException {
        // Load the fourth sample files
        String sampleJson4 = Files.readString(Paths.get("src/main/resources/request_sample4.json"));
        String expectedOutput4 = Files.readString(Paths.get("src/main/resources/output_sample4.txt")).trim();

        String actualOutput4 = converter.convertToBinary(sampleJson4);

        System.out.println("=== SAMPLE 4 TEST ===");
        System.out.println("Expected: " + expectedOutput4);
        System.out.println("Actual:   " + actualOutput4);
        System.out.println("Length expected: " + expectedOutput4.length());
        System.out.println("Length actual:   " + actualOutput4.length());

        // Compare byte by byte if different
        if (!expectedOutput4.equals(actualOutput4)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput4.length(), actualOutput4.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput4.substring(i, Math.min(i + 2, expectedOutput4.length()));
                String actualByte = actualOutput4.substring(i, Math.min(i + 2, actualOutput4.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput4.length() != actualOutput4.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput4.length() + ", actual: " + actualOutput4.length());
            }
        }

        assertEquals(expectedOutput4, actualOutput4);
    }

    @Test
    void testConvertEmptyJson() {
        assertThrows(Exception.class, () -> {
            converter.convertToBinary("{}");
        });
    }
}
