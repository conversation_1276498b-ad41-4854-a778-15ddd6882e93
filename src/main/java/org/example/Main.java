package org.example;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class Main {
    private static final String VERSION = "1.0.0";

    public static void main(String[] args) {
        if (args.length == 0) {
            printUsage();
            return;
        }

        // Handle help and version flags
        if (args[0].equals("--help") || args[0].equals("-h")) {
            printHelp();
            return;
        }

        if (args[0].equals("--version") || args[0].equals("-v")) {
            System.out.println("Card Payment Binary Converter v" + VERSION);
            return;
        }

        // Handle test mode
        if (args[0].equals("--test")) {
            runTestMode();
            return;
        }

        // Handle conversion mode
        if (args.length < 2) {
            System.err.println("Error: Both input and output files are required.");
            printUsage();
            System.exit(1);
        }

        String inputFile = args[0];
        String outputFile = args[1];

        try {
            convertFile(inputFile, outputFile);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            System.exit(1);
        }
    }

    private static void printUsage() {
        System.out.println("Usage: java -jar card-payment-converter.jar [OPTIONS] <input.json> <output.txt>");
        System.out.println("       java -jar card-payment-converter.jar --test");
        System.out.println("       java -jar card-payment-converter.jar --help");
        System.out.println("       java -jar card-payment-converter.jar --version");
    }

    private static void printHelp() {
        System.out.println("Card Payment Binary Converter v" + VERSION);
        System.out.println();
        System.out.println("Converts card payment requests from JSON format to proprietary binary format.");
        System.out.println();
        printUsage();
        System.out.println();
        System.out.println("Arguments:");
        System.out.println("  <input.json>   Input JSON file containing payment request");
        System.out.println("  <output.txt>   Output file to write hex-encoded binary data");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  --test, -t     Run test mode with built-in samples");
        System.out.println("  --help, -h     Show this help message");
        System.out.println("  --version, -v  Show version information");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar card-payment-converter.jar request.json output.txt");
        System.out.println("  java -jar card-payment-converter.jar --test");
        System.out.println();
        System.out.println("Input JSON format:");
        System.out.println("  {");
        System.out.println("    \"timestamp\": \"2025-05-31T11:55:39.000Z\",");
        System.out.println("    \"amount\": { \"value\": 1000, \"currency\": \"GBP\" },");
        System.out.println("    \"card\": {");
        System.out.println("      \"number\": \"****************\",");
        System.out.println("      \"expiry_month\": 12,");
        System.out.println("      \"expiry_year\": 2025,");
        System.out.println("      \"cvc\": \"123\"");
        System.out.println("    },");
        System.out.println("    \"merchant\": {");
        System.out.println("      \"name\": \"Merchant Name\",");
        System.out.println("      \"address\": {");
        System.out.println("        \"line1\": \"Merchant Street\",");
        System.out.println("        \"line2\": \"London\",");
        System.out.println("        \"postcode\": \"SW18 4GG\"");
        System.out.println("      }");
        System.out.println("    }");
        System.out.println("  }");
    }

    private static void convertFile(String inputFile, String outputFile) throws IOException {
        Path inputPath = Paths.get(inputFile);
        Path outputPath = Paths.get(outputFile);

        // Validate input file exists
        if (!Files.exists(inputPath)) {
            throw new IOException("Input file does not exist: " + inputFile);
        }

        // Read input JSON
        System.out.println("Reading input file: " + inputFile);
        String jsonInput = Files.readString(inputPath);

        // Convert to binary
        System.out.println("Converting to binary format...");
        BinaryConverter converter = new BinaryConverter();
        String hexOutput = converter.convertToBinary(jsonInput);

        // Write output
        System.out.println("Writing output file: " + outputFile);
        Files.writeString(outputPath, hexOutput);

        System.out.println("✅ Conversion completed successfully!");
        System.out.println("Output: " + hexOutput);
        System.out.println("Length: " + hexOutput.length() + " characters (" + (hexOutput.length() / 2) + " bytes)");
    }

    private static void runTestMode() {
        try {
            BinaryConverter converter = new BinaryConverter();

            System.out.println("=== Card Payment Binary Converter - Test Mode ===");
            System.out.println("Running tests with built-in samples...");
            System.out.println();

            // Test Sample 1
            System.out.println("--- Sample 1 (16-digit card, GBP, CVC) ---");
            testSample(converter, "src/main/resources/request_sample.json",
                      "src/main/resources/output_sample.txt");

            // Test Sample 2
            System.out.println("\n--- Sample 2 (5-digit card, USD, CVC) ---");
            testSample(converter, "src/main/resources/request_sample2.json",
                      "src/main/resources/output_sample2.txt");

            // Test Sample 3
            System.out.println("\n--- Sample 3 (5-digit card, EUR, no CVC) ---");
            testSample(converter, "src/main/resources/request_sample3.json",
                      "src/main/resources/output_sample3.txt");

            // Test Sample 4
            System.out.println("\n--- Sample 4 (5-digit card, GBP, no CVC) ---");
            testSample(converter, "src/main/resources/request_sample4.json",
                      "src/main/resources/output_sample4.txt");

            System.out.println("\n✅ All tests completed!");

        } catch (IOException e) {
            System.err.println("Error running tests: " + e.getMessage());
            System.exit(1);
        }
    }

    private static void testSample(BinaryConverter converter, String inputFile, String outputFile)
            throws IOException {
        String jsonInput = Files.readString(Paths.get(inputFile));
        String binaryOutput = converter.convertToBinary(jsonInput);
        String expectedOutput = Files.readString(Paths.get(outputFile)).trim();

        boolean matches = binaryOutput.equals(expectedOutput);
        System.out.println("Input: " + inputFile);
        System.out.println("Output: " + binaryOutput);
        System.out.println("Expected: " + expectedOutput);
        System.out.println("Result: " + (matches ? "✅ PASS" : "❌ FAIL"));
    }
}