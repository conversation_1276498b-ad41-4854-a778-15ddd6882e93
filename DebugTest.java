import org.example.BinaryConverter;
import org.example.model.PaymentRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.lang.reflect.Method;

public class DebugTest {
    public static void main(String[] args) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());

        String json = Files.readString(Paths.get("src/main/resources/request_sample3.json"));
        PaymentRequest request = mapper.readValue(json, PaymentRequest.class);

        System.out.println("Card number: " + request.getCard().getNumber());
        System.out.println("Card number length: " + request.getCard().getNumber().length());
        String cvc = request.getCard().getCvc();
        System.out.println("CVC: '" + cvc + "'");
        System.out.println("CVC is null: " + (cvc == null));
        System.out.println("CVC is empty: " + (cvc != null && cvc.isEmpty()));
        System.out.println("CVC equals 'null': " + "null".equals(cvc));

        // Test hasCvcPresent logic
        boolean hasCvc = cvc != null && !cvc.isEmpty();
        System.out.println("hasCvcPresent: " + hasCvc);

        // Test the currency code calculation directly using reflection
        BinaryConverter converter = new BinaryConverter();
        Method method = BinaryConverter.class.getDeclaredMethod("calculateCurrencyCodeWithContext",
            String.class, int.class, boolean.class, String.class, String.class);
        method.setAccessible(true);

        String currency = request.getAmount().getCurrency();
        int cardLength = request.getCard().getNumber().length();
        String merchantName = request.getMerchant().getName();
        String addressLine2 = request.getMerchant().getAddress().getLine2();

        System.out.println("Calling calculateCurrencyCodeWithContext with:");
        System.out.println("  currency: " + currency);
        System.out.println("  cardLength: " + cardLength);
        System.out.println("  hasCvc: " + hasCvc);
        System.out.println("  merchantName: " + merchantName);
        System.out.println("  addressLine2: " + addressLine2);

        int currencyCode = (Integer) method.invoke(converter, currency, cardLength, hasCvc, merchantName, addressLine2);
        System.out.println("Currency code result: 0x" + Integer.toHexString(currencyCode));
        System.out.println("Expected: 0x1E");

        String result = converter.convertToBinary(request);
        System.out.println("Full result: " + result);
    }
}
