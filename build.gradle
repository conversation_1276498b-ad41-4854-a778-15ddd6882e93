plugins {
    id 'java'
    id 'application'
}

group = 'org.example'
version = '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

test {
    useJUnitPlatform()
}

application {
    mainClass = 'org.example.Main'
}

// Configure the main JAR
jar {
    archiveBaseName = 'card-payment-converter'
    archiveVersion = '1.0.0'

    manifest {
        attributes(
            'Main-Class': 'org.example.Main',
            'Implementation-Title': 'Card Payment Binary Converter',
            'Implementation-Version': '1.0.0',
            'Implementation-Vendor': 'Augment Code'
        )
    }
}

// Create a fat JAR that includes all dependencies
task fatJar(type: Jar) {
    archiveBaseName = 'card-payment-converter'
    archiveVersion = '1.0.0'
    archiveClassifier = 'all'

    manifest {
        attributes(
            'Main-Class': 'org.example.Main',
            'Implementation-Title': 'Card Payment Binary Converter',
            'Implementation-Version': '1.0.0',
            'Implementation-Vendor': 'Augment Code'
        )
    }

    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }

    with jar

    // Exclude duplicate files
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

// Make fatJar depend on classes
fatJar.dependsOn classes

// Add a task to build the distributable JAR
task buildCli {
    dependsOn fatJar
    description = 'Builds the CLI tool as a runnable JAR'
    group = 'distribution'

    doLast {
        println "✅ CLI tool built successfully!"
        println "JAR location: ${fatJar.archiveFile.get().asFile.absolutePath}"
        println "Run with: java -jar ${fatJar.archiveFile.get().asFile.name}"
    }
}