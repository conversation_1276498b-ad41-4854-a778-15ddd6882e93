# Card Payment Binary Converter - CLI Tool

A command-line tool for converting card payment requests from JSON format to proprietary binary format.

## Installation

### Prerequisites
- Java 11 or higher

### Download
Download the latest JAR file from the releases or build it yourself:

```bash
./gradlew buildCli
```

The JAR file will be created at: `build/libs/card-payment-converter-1.0.0-all.jar`

## Usage

### Basic Conversion
```bash
java -jar card-payment-converter-1.0.0-all.jar input.json output.txt
```

### Options
```bash
# Show help
java -jar card-payment-converter-1.0.0-all.jar --help

# Show version
java -jar card-payment-converter-1.0.0-all.jar --version

# Run test mode with built-in samples
java -jar card-payment-converter-1.0.0-all.jar --test
```

## Input Format

The input JSON file should contain a payment request in the following format:

```json
{
  "timestamp": "2025-05-31T11:55:39.000Z",
  "amount": {
    "value": 1000,
    "currency": "GBP"
  },
  "card": {
    "number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvc": "123"
  },
  "merchant": {
    "name": "Merchant Name",
    "address": {
      "line1": "Merchant Street",
      "line2": "London",
      "postcode": "SW18 4GG"
    }
  }
}
```

### Field Requirements

- **timestamp**: ISO 8601 format (required)
- **amount.value**: Integer amount (required)
- **amount.currency**: "GBP", "USD", or "EUR" (required)
- **card.number**: Card number string (required)
- **card.expiry_month**: Month 1-12 (required)
- **card.expiry_year**: 4-digit year (required)
- **card.cvc**: CVC string (optional)
- **merchant.name**: Merchant name (required)
- **merchant.address.line1**: Address line 1 (required)
- **merchant.address.line2**: Address line 2 (required)
- **merchant.address.postcode**: Postcode (required)

## Output Format

The output file will contain a single line with the hex-encoded binary data:

```
10****************250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826
```

## Examples

### Example 1: Basic Usage
```bash
# Create input file
cat > payment_request.json << EOF
{
  "timestamp": "2025-05-31T11:55:39.000Z",
  "amount": { "value": 1000, "currency": "GBP" },
  "card": {
    "number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvc": "123"
  },
  "merchant": {
    "name": "Merchant Name",
    "address": {
      "line1": "Merchant Street",
      "line2": "London",
      "postcode": "SW18 4GG"
    }
  }
}
EOF

# Convert to binary
java -jar card-payment-converter-1.0.0-all.jar payment_request.json binary_output.txt

# View result
cat binary_output.txt
```

### Example 2: Test Mode
```bash
# Run built-in tests
java -jar card-payment-converter-1.0.0-all.jar --test
```

## Error Handling

The tool provides clear error messages for common issues:

- **File not found**: `Error: Input file does not exist: filename.json`
- **Invalid JSON**: `Error: Failed to parse JSON: ...`
- **Missing fields**: `Error: Required field missing: ...`
- **Invalid currency**: `Error: Unsupported currency: XYZ`

## Exit Codes

- **0**: Success
- **1**: Error (invalid input, file not found, conversion failure)

## Building from Source

```bash
# Clone the repository
git clone <repository-url>
cd guesser

# Build the CLI tool
./gradlew buildCli

# The JAR will be created at:
# build/libs/card-payment-converter-1.0.0-all.jar
```

## Supported Platforms

The CLI tool runs on any platform with Java 11+ installed:
- Windows
- macOS
- Linux
- Unix

## License

[Add your license information here]
